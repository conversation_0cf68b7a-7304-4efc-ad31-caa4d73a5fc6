/* Search Results CSS */

:root {
  --primary-color: #4a4a4a;
  --secondary-color: #0366d6;
  --accent-color: #f6f8fa;
  --border-color: #e1e4e8;
  --highlight-color: rgba(255, 255, 0, 0.2);
  --highlight-hover-color: rgba(255, 255, 0, 0.4);
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --text-color: #24292e;
  --light-text-color: #586069;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --transition-speed: 0.3s;
}

/* Card styles */
.search-card {
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  transition: box-shadow var(--transition-speed);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.search-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.search-card-header {
  padding: 1rem;
  background-color: var(--accent-color);
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
}

.search-card-body {
  padding: 1.25rem;
  background-color: #fff;
}

/* Typography */
.response-container {
  line-height: 1.6;
  color: var(--text-color);
}

.response-container p {
  margin-bottom: 1rem;
}

.response-container h1,
.response-container h2,
.response-container h3,
.response-container h4,
.response-container h5,
.response-container h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-color);
}

/* Source items */
.sources-container {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.source-item {
  padding: 0.75rem;
  border-radius: 4px;
  border-left: 3px solid transparent;
  transition: all var(--transition-speed);
  margin-bottom: 0.5rem;
}

.source-item:hover {
  background-color: var(--accent-color);
  border-left-color: var(--secondary-color);
  transform: translateX(3px);
}

.source-item.active {
  background-color: var(--accent-color);
  border-left-color: var(--secondary-color);
}

.source-title {
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.source-content {
  color: var(--text-color);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.source-metadata {
  color: var(--light-text-color);
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

/* Citation and highlight styles */
.citation-highlight {
  background-color: var(--highlight-color);
  border-bottom: 1px dashed var(--warning-color);
  cursor: pointer;
  transition: background-color var(--transition-speed);
  padding: 0 2px;
}

.citation-highlight:hover {
  background-color: var(--highlight-hover-color);
}

.citation-number {
  font-size: 0.8em;
  vertical-align: super;
  color: var(--secondary-color);
  font-weight: bold;
  margin-left: 2px;
  cursor: pointer;
  text-decoration: none;
}

.citation-number:hover {
  text-decoration: underline;
}

/* Metrics display */
.metrics-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.5rem;
  gap: 1rem;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--accent-color);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  min-width: 100px;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--secondary-color);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--light-text-color);
  text-transform: uppercase;
}

/* Relevance score visualization */
.relevance-bar {
  height: 6px;
  border-radius: 3px;
  width: 100%;
  background: linear-gradient(90deg, var(--success-color) 0%, var(--warning-color) 60%, var(--danger-color) 100%);
  position: relative;
  margin-top: 4px;
}

.relevance-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #fff;
  border: 2px solid #333;
  border-radius: 50%;
  top: -1px;
  transform: translateX(-50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Source type badges */
.badge {
  padding: 0.2em 0.6em;
  font-size: 0.75em;
  font-weight: 500;
  border-radius: 10px;
  white-space: nowrap;
}

.source-badge-slack {
  background-color: #4A154B !important;
  color: white;
}

.source-badge-github {
  background-color: #24292E !important;
  color: white;
}

.source-badge-confluence {
  background-color: #0052CC !important;
  color: white;
}

.source-badge-jira {
  background-color: #0052CC !important;
  color: white;
}

.source-badge-google {
  background-color: #4285F4 !important;
  color: white;
}

.source-badge-email {
  background-color: #D14836 !important;
  color: white;
}

.source-badge-web {
  background-color: #607D8B !important;
  color: white;
}

/* Follow-up questions */
.follow-up-container {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.follow-up-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.75rem 0;
}

.follow-up-suggestion {
  background-color: var(--accent-color);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-speed);
  white-space: nowrap;
}

.follow-up-suggestion:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
}

/* Spinner for loading state */
.loading-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--secondary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced Professional UI Overrides */
.search-card {
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #e9ecef !important;
  margin-bottom: 1.5rem !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.search-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15) !important;
  border-color: #007bff !important;
}

.search-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 2rem !important;
  border-bottom: none !important;
  position: relative !important;
  overflow: hidden !important;
}

.user-query {
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.query-timestamp {
  opacity: 0.9 !important;
  font-size: 0.9rem !important;
  margin-top: 0.5rem !important;
}

.assistant-response {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  border-left: 4px solid #007bff !important;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05) !important;
}

.response-content {
  line-height: 1.8 !important;
  margin: 0 !important;
  font-size: 1.05rem !important;
  color: #2c3e50 !important;
}

.confidence-indicator {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.6rem 1.2rem !important;
  border-radius: 25px !important;
  font-size: 0.9rem !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.confidence-high {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
  color: #155724 !important;
  border: 1px solid #c3e6cb !important;
}
.confidence-medium {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
  color: #856404 !important;
  border: 1px solid #ffeaa7 !important;
}
.confidence-low {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
  color: #721c24 !important;
  border: 1px solid #f5c6cb !important;
}

.export-options {
  margin-top: 1.5rem !important;
  padding-top: 1.5rem !important;
  border-top: 1px solid #e9ecef !important;
}

.export-btn {
  margin-right: 0.75rem !important;
  margin-bottom: 0.75rem !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.export-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced Markdown Response Styling */
.response-container .response-heading {
  color: #2c3e50 !important;
  font-weight: 500 !important;
  margin-top: 1.5rem !important;
  margin-bottom: 0.75rem !important;
  border-bottom: 1px solid #e9ecef !important;
  padding-bottom: 0.5rem !important;
  line-height: 1.3 !important;
}

.response-container h1.response-heading {
  font-size: 1.6rem !important;
  color: #1a202c !important;
  font-weight: 500 !important;
}

.response-container h2.response-heading {
  font-size: 1.35rem !important;
  color: #2d3748 !important;
  font-weight: 500 !important;
}

.response-container h3.response-heading {
  font-size: 1.15rem !important;
  color: #4a5568 !important;
  font-weight: 500 !important;
}

.response-container .response-list {
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
}

.response-container .response-list li {
  margin-bottom: 0.5rem !important;
  line-height: 1.6 !important;
}

.response-container .response-list li strong {
  color: #2c3e50 !important;
  font-weight: 600 !important;
}

.response-container .response-table {
  margin: 1.5rem 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.response-container .response-code {
  background-color: #f8f9fa !important;
  color: #e83e8c !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 4px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.9rem !important;
}

.response-container .response-pre {
  background-color: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  margin: 1rem 0 !important;
  overflow-x: auto !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.9rem !important;
  line-height: 1.4 !important;
}

.response-container p {
  margin-bottom: 1rem !important;
  line-height: 1.7 !important;
  color: #2c3e50 !important;
}

.response-container strong {
  color: #1a202c !important;
  font-weight: 600 !important;
}

.response-container em {
  color: #4a5568 !important;
  font-style: italic !important;
}

.response-container a {
  color: #007bff !important;
  text-decoration: none !important;
  border-bottom: 1px solid transparent !important;
  transition: all 0.2s ease !important;
}

.response-container a:hover {
  color: #0056b3 !important;
  border-bottom-color: #0056b3 !important;
}

/* Citation styling within response */
.response-container .citation-number {
  display: inline-block !important;
  background: #007bff !important;
  color: white !important;
  font-size: 0.75rem !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 50% !important;
  text-decoration: none !important;
  margin-left: 0.25rem !important;
  font-weight: 600 !important;
  min-width: 1.5rem !important;
  text-align: center !important;
  transition: all 0.2s ease !important;
}

/* SIMPLE CLEAN RESPONSE FORMATTING */

/* Simple Paragraphs */
.response-paragraph {
  margin-bottom: 0.5rem !important;
  line-height: 1.6 !important;
  color: #2c3e50 !important;
  font-size: 1rem !important;
  text-align: left !important;
}

/* Simple Clean Lists */
.response-list, .professional-list {
  margin: 0.5rem 0 !important;
  padding-left: 1.5rem !important;
  list-style: none !important;
}

.response-list-item {
  margin-bottom: 0.5rem !important;
  line-height: 1.5 !important;
  color: #2c3e50 !important;
  position: relative !important;
  padding-left: 0 !important;
}

.response-list-item:last-child {
  margin-bottom: 0 !important;
}

/* Simple bullet points */
.response-list-item:before {
  content: "•" !important;
  color: #007bff !important;
  font-weight: bold !important;
  position: absolute !important;
  left: -1rem !important;
}

/* Enhanced emphasis styling for detailed responses */
.response-emphasis {
  font-weight: 600 !important;
  color: #1a202c !important;
  background: none !important;
  padding: 0 !important;
}

/* Hide headers - we want simple bullet points only */
.response-heading, .response-h1, .response-h2, .response-h3, .response-h4, .response-h5, .response-h6 {
  display: none !important;
}

/* Professional Tables */
.professional-table {
  margin: 2rem 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  border: none !important;
}

.professional-table th {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 1rem !important;
  border: none !important;
}

.professional-table td {
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid #e9ecef !important;
  vertical-align: top !important;
}

.professional-table tbody tr:hover {
  background-color: #f8f9fa !important;
}

/* Professional Code Blocks */
.response-code {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  color: #e83e8c !important;
  padding: 0.3rem 0.6rem !important;
  border-radius: 6px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.9rem !important;
  border: 1px solid #dee2e6 !important;
  font-weight: 500 !important;
}

.response-pre {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
  border: 2px solid #e9ecef !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  margin: 2rem 0 !important;
  overflow-x: auto !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
}

/* Response Container Enhancements */
.response-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border-radius: 16px !important;
  padding: 2.5rem !important;
  margin: 1.5rem 0 !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06) !important;
  position: relative !important;
}

.response-container::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, #007bff 0%, #6610f2 50%, #e83e8c 100%) !important;
  border-radius: 16px 16px 0 0 !important;
}

/* Professional Links */
.response-container a {
  color: #007bff !important;
  text-decoration: none !important;
  border-bottom: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
}

.response-container a:hover {
  color: #0056b3 !important;
  border-bottom-color: #0056b3 !important;
  background: rgba(0, 123, 255, 0.1) !important;
  padding: 0.1rem 0.3rem !important;
  border-radius: 4px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .response-container {
    padding: 1.5rem !important;
    margin: 1rem 0 !important;
  }

  .response-h1 {
    font-size: 1.5rem !important;
  }

  .response-h2 {
    font-size: 1.3rem !important;
  }

  .response-h3 {
    font-size: 1.1rem !important;
  }

  .professional-list {
    padding: 0.75rem 0.75rem 0.75rem 1.5rem !important;
  }
}

.response-container .citation-number:hover {
  background: #0056b3 !important;
  transform: scale(1.1) !important;
}

/* PROFESSIONAL UI ENHANCEMENTS */

/* Professional Response Container */
.professional-response-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border-radius: 16px !important;
  padding: 2.5rem !important;
  margin: 1.5rem 0 !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06) !important;
  position: relative !important;
}

.professional-response-container::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: linear-gradient(90deg, #007bff 0%, #6610f2 50%, #e83e8c 100%) !important;
  border-radius: 16px 16px 0 0 !important;
}

.response-content {
  line-height: 1.8 !important;
  color: #2c3e50 !important;
  font-size: 1.05rem !important;
}

.response-content p {
  margin-bottom: 1.2rem !important;
  text-align: left !important;
}

.response-content ul, .response-content ol {
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
}

.response-content li {
  margin-bottom: 0.5rem !important;
  line-height: 1.6 !important;
}

.response-content strong {
  color: #1a202c !important;
  font-weight: 600 !important;
}

/* Professional Sources Section */
.professional-sources-section {
  margin-top: 3rem !important;
  padding-top: 2rem !important;
  border-top: 2px solid #e9ecef !important;
}

.sources-header {
  margin-bottom: 2rem !important;
  text-align: center !important;
}

.sources-title {
  color: #2c3e50 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  font-size: 1.5rem !important;
}

.sources-subtitle {
  color: #6c757d !important;
  font-size: 0.95rem !important;
  margin: 0 !important;
}

.professional-sources-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
  gap: 1.5rem !important;
  margin-top: 1.5rem !important;
}

/* Professional Source Cards */
.professional-source-card {
  background: white !important;
  border-radius: 12px !important;
  border: 2px solid #e9ecef !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  overflow: hidden !important;
}

.professional-source-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15) !important;
  border-color: #007bff !important;
}

.professional-source-card.source-active {
  border-color: #007bff !important;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25) !important;
  transform: translateY(-3px) !important;
}

.source-card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  padding: 1.25rem !important;
  border-bottom: 1px solid #dee2e6 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.citation-number-large {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
}

.source-type-info {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  gap: 0.5rem !important;
}

.professional-source-badge {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.4rem 0.8rem !important;
  border-radius: 20px !important;
  font-size: 0.8rem !important;
  font-weight: 600 !important;
  color: white !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.relevance-score {
  font-size: 0.8rem !important;
  color: #6c757d !important;
}

.relevance-label {
  font-weight: 500 !important;
}

.relevance-value {
  font-weight: 700 !important;
  color: #007bff !important;
}

.source-card-body {
  padding: 1.5rem !important;
}

.source-document-title {
  color: #2c3e50 !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
  font-size: 1rem !important;
  line-height: 1.4 !important;
}

.source-content-preview {
  color: #495057 !important;
  line-height: 1.6 !important;
  margin-bottom: 1rem !important;
  font-size: 0.9rem !important;
}

.source-date {
  color: #6c757d !important;
  font-size: 0.8rem !important;
  display: flex !important;
  align-items: center !important;
}

.source-card-footer {
  padding: 1rem 1.5rem !important;
  background-color: #f8f9fa !important;
  border-top: 1px solid #dee2e6 !important;
}

.professional-source-link {
  color: #007bff !important;
  text-decoration: none !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
  display: inline-flex !important;
  align-items: center !important;
  transition: all 0.2s ease !important;
}

.professional-source-link:hover {
  color: #0056b3 !important;
  text-decoration: underline !important;
}

/* Enhanced Citation Numbers in Response */
.response-content .citation-number {
  display: inline-block !important;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  font-size: 0.75rem !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 12px !important;
  text-decoration: none !important;
  margin-left: 0.25rem !important;
  font-weight: 600 !important;
  min-width: 1.5rem !important;
  text-align: center !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
}

.response-content .citation-number:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
}

.citation-highlight {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 6px !important;
  border: 1px solid #ffeaa7 !important;
  animation: highlightPulse 2s ease-in-out !important;
}

@keyframes highlightPulse {
  0%, 100% { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); }
  50% { background: linear-gradient(135deg, #ffeaa7 0%, #ffdf7e 100%); }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .professional-sources-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .professional-response-container {
    padding: 1.5rem !important;
    margin: 1rem 0 !important;
  }

  .source-card-header {
    padding: 1rem !important;
  }

  .source-card-body {
    padding: 1rem !important;
  }

  .citation-number-large {
    width: 35px !important;
    height: 35px !important;
    font-size: 1rem !important;
  }

  .sources-title {
    font-size: 1.3rem !important;
  }

  .metrics-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .metric-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }

  .follow-up-suggestions {
    flex-direction: column;
  }

  .follow-up-suggestion {
    width: 100%;
    text-align: center;
  }

  .search-card-header {
    padding: 1.5rem !important;
  }

  .search-card-body {
    padding: 1.5rem !important;
  }

  .assistant-response {
    padding: 1.5rem !important;
  }

  .user-query {
    font-size: 1.1rem !important;
  }

  .response-container h1.response-heading {
    font-size: 1.5rem !important;
  }

  .response-container h2.response-heading {
    font-size: 1.3rem !important;
  }

  .response-container h3.response-heading {
    font-size: 1.1rem !important;
  }

  .response-container .response-pre {
    font-size: 0.8rem !important;
    padding: 0.75rem !important;
  }
}